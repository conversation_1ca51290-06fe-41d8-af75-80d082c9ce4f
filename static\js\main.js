// 添加学生
document.getElementById('addStudentForm')?.addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = {
        name: document.getElementById('name').value,
        gender: document.getElementById('gender').value,
        birthdate: document.getElementById('birthdate').value,
        class: document.getElementById('class').value
    };
    
    try {
        const response = await fetch('/add_student', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        if (result.success) {
            alert('学生添加成功!');
            window.location.reload();
        }
    } catch (error) {
        console.error('Error:', error);
    }
});

// 删除学生
document.querySelectorAll('.delete-btn').forEach(btn => {
    btn.addEventListener('click', async function() {
        const studentId = this.getAttribute('data-id');
        
        if (confirm('确定要删除这个学生吗?')) {
            try {
                const response = await fetch(`/delete_student/${studentId}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                if (result.success) {
                    alert('学生删除成功!');
                    window.location.reload();
                }
            } catch (error) {
                console.error('Error:', error);
            }
        }
    });
});