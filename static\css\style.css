/* 基础样式 */
body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    color: #333;
}

header {
    background: #35424a;
    color: #ffffff;
    padding: 20px 0;
    text-align: center;
}

nav ul {
    padding: 0;
    list-style: none;
    background: #e8491d;
    text-align: center;
    margin: 0;
}

nav ul li {
    display: inline;
}

nav ul li a {
    color: #ffffff;
    padding: 15px 20px;
    display: inline-block;
    text-decoration: none;
}

nav ul li a:hover {
    background: #cccccc;
    color: #35424a;
}

main {
    padding: 20px;
}

footer {
    background: #35424a;
    color: #ffffff;
    text-align: center;
    padding: 20px;
    margin-top: 20px;
}

/* 容器样式 */
.container {
    display: flex;
    gap: 20px;
}

.form-container, .table-container {
    flex: 1;
    background: #f4f4f4;
    padding: 20px;
    border-radius: 5px;
}

/* 表格样式 */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

table, th, td {
    border: 1px solid #ddd;
}

th, td {
    padding: 12px;
    text-align: left;
}

th {
    background-color: #35424a;
    color: white;
}

tr:nth-child(even) {
    background-color: #f2f2f2;
}

/* 表单样式 */
.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

input[type="text"],
input[type="date"],
select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

button {
    background: #35424a;
    color: #fff;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    border-radius: 4px;
}

button:hover {
    background: #e8491d;
}

.delete-btn {
    background: #e8491d;
}

.delete-btn:hover {
    background: #35424a;
}