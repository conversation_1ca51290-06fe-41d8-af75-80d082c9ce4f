{% extends "base.html" %}

{% block title %}挂号单管理{% endblock %}

{% block content %}
<div class="table-container">
    <h2>挂号单列表</h2>
    <div class="mb-3">
        <a href="{{ url_for('add_registration') }}" class="btn btn-success">添加挂号单</a>
    </div>
    
    <table class="table table-striped table-hover">
        <thead class="table-light">
            <tr>
                <th>ID</th>
                <th>患者姓名</th>
                <th>医生</th>
                <th>门诊</th>
                <th>挂号时间</th>
                <th>状态</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for reg in registrations %}
            <tr>
                <td>{{ reg[0] }}</td>
                <td>{{ reg[1] }}</td>
                <td>{{ reg[6] }}</td>
                <td>{{ reg[7] }}</td>
                <td>{{ reg[4] }}</td>
                <td>{{ reg[5] }}</td>
                <td>
                    <a href="{{ url_for('edit_registration', id=reg[0]) }}" class="btn btn-sm btn-primary">编辑</a>
                    <a href="{{ url_for('delete_registration', id=reg[0]) }}" class="btn btn-sm btn-danger" onclick="return confirm('确定删除吗？')">删除</a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}