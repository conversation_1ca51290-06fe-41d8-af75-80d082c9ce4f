{% extends "base.html" %}

{% block title %}门诊管理{% endblock %}

{% block content %}
<div class="table-container">
    <h2>门诊列表</h2>
    <div class="mb-3">
        <a href="{{ url_for('add_clinic') }}" class="btn btn-success">添加门诊</a>
    </div>
    
    <table class="table table-striped table-hover">
        <thead class="table-light">
            <tr>
                <th>ID</th>
                <th>名称</th>
                <th>位置</th>
                <th>开放时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for clinic in clinics %}
            <tr>
                <td>{{ clinic[0] }}</td>
                <td>{{ clinic[1] }}</td>
                <td>{{ clinic[2] }}</td>
                <td>{{ clinic[3] }}</td>
                <td>
                    <a href="{{ url_for('edit_clinic', id=clinic[0]) }}" class="btn btn-sm btn-primary">编辑</a>
                    <a href="{{ url_for('delete_clinic', id=clinic[0]) }}" class="btn btn-sm btn-danger" onclick="return confirm('确定删除吗？')">删除</a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}