-- 创建数据库（如果不存在）
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'HospitalOutpatient')
BEGIN
    CREATE DATABASE HospitalOutpatient;
END
GO

USE HospitalOutpatient;
GO

-- 创建医师表
CREATE TABLE Doctors (
    DoctorID INT PRIMARY KEY IDENTITY(1,1),
    Name NVARCHAR(50) NOT NULL,
    Specialty NVARCHAR(100),
    Title NVARCHAR(50),
    Contact NVARCHAR(50)
);
GO

-- 创建药品表
CREATE TABLE Medicines (
    MedicineID INT PRIMARY KEY IDENTITY(1,1),
    Name NVARCHAR(100) NOT NULL,
    Specification NVARCHAR(100),
    Manufacturer NVARCHAR(100),
    Price DECIMAL(10, 2),
    Stock INT
);
GO

-- 创建门诊表
CREATE TABLE OutpatientClinics (
    ClinicID INT PRIMARY KEY IDENTITY(1,1),
    Name NVARCHAR(100) NOT NULL,
    Location NVARCHAR(100),
    OpeningHours NVARCHAR(100)
);
GO

-- 创建挂号单表
CREATE TABLE Registrations (
    RegistrationID INT PRIMARY KEY IDENTITY(1,1),
    PatientName NVARCHAR(50) NOT NULL,
    DoctorID INT FOREIGN KEY REFERENCES Doctors(DoctorID),
    ClinicID INT FOREIGN KEY REFERENCES OutpatientClinics(ClinicID),
    RegistrationTime DATETIME,
    Status NVARCHAR(20)
);
GO

-- 插入医师数据
INSERT INTO Doctors (Name, Specialty, Title, Contact)
VALUES
    ('张医生', '心血管内科', '主任医师', '13800138000'),
    ('李医生', '神经内科', '副主任医师', '13900139000');
GO

-- 插入药品数据
INSERT INTO Medicines (Name, Specification, Manufacturer, Price, Stock)
VALUES
    ('阿司匹林', '100mg*30片', '拜耳医药', 15.50, 100),
    ('布洛芬', '0.3g*24片', '华北制药', 12.00, 200);
GO

-- 插入门诊数据
INSERT INTO OutpatientClinics (Name, Location, OpeningHours)
VALUES
    ('心血管内科', '门诊大楼3楼', '周一至周五 8:00-17:00'),
    ('神经内科', '门诊大楼4楼', '周一至周五 8:00-17:00');
GO

-- 插入挂号单数据（使用正确的ID引用）
INSERT INTO Registrations (PatientName, DoctorID, ClinicID, RegistrationTime, Status)
VALUES
    ('李四', 1, 1, '2025-07-26 09:30:00', '待就诊'),
    ('王五', 2, 2, '2025-07-26 10:00:00', '已完成');
GO

-- 验证数据插入结果
SELECT 'Doctors' AS TableName, COUNT(*) AS RecordCount FROM Doctors
UNION ALL
SELECT 'Medicines', COUNT(*) FROM Medicines
UNION ALL
SELECT 'OutpatientClinics', COUNT(*) FROM OutpatientClinics
UNION ALL
SELECT 'Registrations', COUNT(*) FROM Registrations;
GO