-- 创建数据库
CREATE DATABASE HospitalOutpatient;
GO

USE HospitalOutpatient;
GO

-- 创建医师表
CREATE TABLE Doctors (
    DoctorID INT PRIMARY KEY IDENTITY(1,1),
    Name NVARCHAR(50) NOT NULL,
    Specialty NVARCHAR(100),
    Title NVARCHAR(50),
    Contact NVARCHAR(50)
);

-- 创建药品表
CREATE TABLE Medicines (
    MedicineID INT PRIMARY KEY IDENTITY(1,1),
    Name NVARCHAR(100) NOT NULL,
    Specification NVARCHAR(100),
    Manufacturer NVARCHAR(100),
    Price DECIMAL(10, 2),
    Stock INT
);

-- 创建门诊表
CREATE TABLE OutpatientClinics (
    ClinicID INT PRIMARY KEY IDENTITY(1,1),
    Name NVARCHAR(100) NOT NULL,
    Location NVARCHAR(100),
    OpeningHours NVARCHAR(100)
);

-- 创建挂号单表
CREATE TABLE Registrations (
    RegistrationID INT PRIMARY KEY IDENTITY(1,1),
    PatientName NVARCHAR(50) NOT NULL,
    DoctorID INT FOREIGN KEY REFERENCES Doctors(DoctorID),
    ClinicID INT FOREIGN KEY REFERENCES OutpatientClinics(ClinicID),
    RegistrationTime DATETIME,
    Status NVARCHAR(20)
);