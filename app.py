from flask import Flask, render_template, request, redirect, url_for, flash, send_file
import pyodbc
import pandas as pd
from io import BytesIO

app = Flask(__name__)
app.secret_key = 'your_secret_key'

# 数据库连接函数
def get_db_connection():
    conn = pyodbc.connect(
        'DRIVER={ODBC Driver 17 for SQL Server};'
        'SERVER=localhost\\SQLEXPRESS;'
        'DATABASE=HospitalOutpatient;'
        'Trusted_Connection=yes;'
    )
    return conn

# 首页
@app.route('/')
def index():
    return render_template('index.html')

# 医生管理
@app.route('/doctors')
def doctors():
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM Doctors')
    doctors = cursor.fetchall()
    conn.close()
    return render_template('doctors.html', doctors=doctors)

@app.route('/add_doctor', methods=['GET', 'POST'])
def add_doctor():
    if request.method == 'POST':
        name = request.form['name']
        specialty = request.form['specialty']
        title = request.form['title']
        contact = request.form['contact']
        
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(
            'INSERT INTO Doctors (Name, Specialty, Title, Contact) VALUES (?, ?, ?, ?)',
            (name, specialty, title, contact)
        )
        conn.commit()
        conn.close()
        flash('医生添加成功')
        return redirect(url_for('doctors'))
    return render_template('add_doctor.html')

@app.route('/edit_doctor/<int:id>', methods=['GET', 'POST'])
def edit_doctor(id):
    if request.method == 'POST':
        name = request.form['name']
        specialty = request.form['specialty']
        title = request.form['title']
        contact = request.form['contact']
        
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(
            'UPDATE Doctors SET Name=?, Specialty=?, Title=?, Contact=? WHERE DoctorID=?',
            (name, specialty, title, contact, id)
        )
        conn.commit()
        conn.close()
        flash('医生信息更新成功')
        return redirect(url_for('doctors'))
    
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM Doctors WHERE DoctorID=?', (id,))
    doctor = cursor.fetchone()
    conn.close()
    return render_template('edit_doctor.html', doctor=doctor)

@app.route('/delete_doctor/<int:id>')
def delete_doctor(id):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('DELETE FROM Doctors WHERE DoctorID=?', (id,))
    conn.commit()
    conn.close()
    flash('医生删除成功')
    return redirect(url_for('doctors'))

# 药品管理
@app.route('/medicines')
def medicines():
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM Medicines')
    medicines = cursor.fetchall()
    conn.close()
    return render_template('medicines.html', medicines=medicines)

@app.route('/add_medicine', methods=['GET', 'POST'])
def add_medicine():
    if request.method == 'POST':
        name = request.form['name']
        specification = request.form['specification']
        manufacturer = request.form['manufacturer']
        price = float(request.form['price'])
        stock = int(request.form['stock'])
        
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(
            'INSERT INTO Medicines (Name, Specification, Manufacturer, Price, Stock) VALUES (?, ?, ?, ?, ?)',
            (name, specification, manufacturer, price, stock)
        )
        conn.commit()
        conn.close()
        flash('药品添加成功')
        return redirect(url_for('medicines'))
    return render_template('add_medicine.html')

@app.route('/edit_medicine/<int:id>', methods=['GET', 'POST'])
def edit_medicine(id):
    if request.method == 'POST':
        name = request.form['name']
        specification = request.form['specification']
        manufacturer = request.form['manufacturer']
        price = float(request.form['price'])
        stock = int(request.form['stock'])
        
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(
            'UPDATE Medicines SET Name=?, Specification=?, Manufacturer=?, Price=?, Stock=? WHERE MedicineID=?',
            (name, specification, manufacturer, price, stock, id)
        )
        conn.commit()
        conn.close()
        flash('药品信息更新成功')
        return redirect(url_for('medicines'))
    
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM Medicines WHERE MedicineID=?', (id,))
    medicine = cursor.fetchone()
    conn.close()
    return render_template('edit_medicine.html', medicine=medicine)

@app.route('/delete_medicine/<int:id>')
def delete_medicine(id):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('DELETE FROM Medicines WHERE MedicineID=?', (id,))
    conn.commit()
    conn.close()
    flash('药品删除成功')
    return redirect(url_for('medicines'))

@app.route('/export_medicines')
def export_medicines():
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM Medicines')
    medicines = cursor.fetchall()
    conn.close()
    
    # 将数据转换为DataFrame
    data = []
    for med in medicines:
        data.append({
            '药品ID': med.MedicineID,
            '药品名称': med.Name,
            '规格': med.Specification,
            '生产厂家': med.Manufacturer,
            '价格': float(med.Price),
            '库存': med.Stock
        })
    
    df = pd.DataFrame(data)
    
    # 创建Excel文件
    output = BytesIO()
    writer = pd.ExcelWriter(output, engine='xlsxwriter')
    df.to_excel(writer, sheet_name='药品列表', index=False)
    writer.close()
    output.seek(0)
    
    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name='药品列表.xlsx'
    )

# 门诊管理
@app.route('/clinics')
def clinics():
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM OutpatientClinics')
    clinics = cursor.fetchall()
    conn.close()
    return render_template('clinics.html', clinics=clinics)

@app.route('/add_clinic', methods=['GET', 'POST'])
def add_clinic():
    if request.method == 'POST':
        name = request.form['name']
        location = request.form['location']
        opening_hours = request.form['opening_hours']
        
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(
            'INSERT INTO OutpatientClinics (Name, Location, OpeningHours) VALUES (?, ?, ?)',
            (name, location, opening_hours)
        )
        conn.commit()
        conn.close()
        flash('门诊添加成功')
        return redirect(url_for('clinics'))
    return render_template('add_clinic.html')

@app.route('/edit_clinic/<int:id>', methods=['GET', 'POST'])
def edit_clinic(id):
    if request.method == 'POST':
        name = request.form['name']
        location = request.form['location']
        opening_hours = request.form['opening_hours']
        
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(
            'UPDATE OutpatientClinics SET Name=?, Location=?, OpeningHours=? WHERE ClinicID=?',
            (name, location, opening_hours, id)
        )
        conn.commit()
        conn.close()
        flash('门诊信息更新成功')
        return redirect(url_for('clinics'))
    
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM OutpatientClinics WHERE ClinicID=?', (id,))
    clinic = cursor.fetchone()
    conn.close()
    return render_template('edit_clinic.html', clinic=clinic)

@app.route('/delete_clinic/<int:id>')
def delete_clinic(id):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('DELETE FROM OutpatientClinics WHERE ClinicID=?', (id,))
    conn.commit()
    conn.close()
    flash('门诊删除成功')
    return redirect(url_for('clinics'))

# 挂号单管理
@app.route('/registrations')
def registrations():
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('''
        SELECT r.*, d.Name AS DoctorName, o.Name AS ClinicName 
        FROM Registrations r
        JOIN Doctors d ON r.DoctorID = d.DoctorID
        JOIN OutpatientClinics o ON r.ClinicID = o.ClinicID
    ''')
    registrations = cursor.fetchall()
    conn.close()
    return render_template('registrations.html', registrations=registrations)

from datetime import datetime

@app.route('/add_registration', methods=['GET', 'POST'])
def add_registration():
    if request.method == 'POST':
        patient_name = request.form['patient_name']
        doctor_id = int(request.form['doctor_id'])
        clinic_id = int(request.form['clinic_id'])
        registration_time_str = request.form['registration_time']
        status = request.form['status']
        
        # 将字符串转换为datetime对象
        registration_time = datetime.strptime(registration_time_str, '%Y-%m-%dT%H:%M')
        
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(
            'INSERT INTO Registrations (PatientName, DoctorID, ClinicID, RegistrationTime, Status) VALUES (?, ?, ?, ?, ?)',
            (patient_name, doctor_id, clinic_id, registration_time, status)
        )
        conn.commit()
        conn.close()
        flash('挂号单添加成功')
        return redirect(url_for('registrations'))
    
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM Doctors')
    doctors = cursor.fetchall()
    cursor.execute('SELECT * FROM OutpatientClinics')
    clinics = cursor.fetchall()
    conn.close()
    return render_template('add_registration.html', doctors=doctors, clinics=clinics)

@app.route('/edit_registration/<int:id>', methods=['GET', 'POST'])
def edit_registration(id):
    if request.method == 'POST':
        patient_name = request.form['patient_name']
        doctor_id = int(request.form['doctor_id'])
        clinic_id = int(request.form['clinic_id'])
        registration_time_str = request.form['registration_time']
        status = request.form['status']
        
        # 将字符串转换为datetime对象
        registration_time = datetime.strptime(registration_time_str, '%Y-%m-%dT%H:%M')
        
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(
            'UPDATE Registrations SET PatientName=?, DoctorID=?, ClinicID=?, RegistrationTime=?, Status=? WHERE RegistrationID=?',
            (patient_name, doctor_id, clinic_id, registration_time, status, id)
        )
        conn.commit()
        conn.close()
        flash('挂号单更新成功')
        return redirect(url_for('registrations'))
    
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM Registrations WHERE RegistrationID=?', (id,))
    registration = cursor.fetchone()
    cursor.execute('SELECT * FROM Doctors')
    doctors = cursor.fetchall()
    cursor.execute('SELECT * FROM OutpatientClinics')
    clinics = cursor.fetchall()
    conn.close()
    return render_template('edit_registration.html', registration=registration, doctors=doctors, clinics=clinics)

@app.route('/delete_registration/<int:id>')
def delete_registration(id):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('DELETE FROM Registrations WHERE RegistrationID=?', (id,))
    conn.commit()
    conn.close()
    flash('挂号单删除成功')
    return redirect(url_for('registrations'))

if __name__ == '__main__':
    app.run(debug=True)