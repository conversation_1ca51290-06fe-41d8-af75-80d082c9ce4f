USE HospitalOutpatient;
GO

-- 清空现有数据（如果有的话）
DELETE FROM Registrations;
DELETE FROM Doctors;
DELETE FROM Medicines;
DELETE FROM OutpatientClinics;
GO

-- 插入医师数据
INSERT INTO Doctors (Name, Specialty, Title, Contact)
VALUES ('张医生', '心血管内科', '主任医师', '13800138000');

INSERT INTO Doctors (Name, Specialty, Title, Contact)
VALUES ('李医生', '神经内科', '副主任医师', '13900139000');
GO

-- 插入药品数据
INSERT INTO Medicines (Name, Specification, Manufacturer, Price, Stock)
VALUES ('阿司匹林', '100mg*30片', '拜耳医药', 15.50, 100);

INSERT INTO Medicines (Name, Specification, Manufacturer, Price, Stock)
VALUES ('布洛芬', '0.3g*24片', '华北制药', 12.00, 200);
GO

-- 插入门诊数据
INSERT INTO OutpatientClinics (Name, Location, OpeningHours)
VALUES ('心血管内科', '门诊大楼3楼', '周一至周五 8:00-17:00');

INSERT INTO OutpatientClinics (Name, Location, OpeningHours)
VALUES ('神经内科', '门诊大楼4楼', '周一至周五 8:00-17:00');
GO

-- 插入挂号单数据
INSERT INTO Registrations (PatientName, DoctorID, ClinicID, RegistrationTime, Status)
VALUES ('李四', 1, 1, '2025-07-26 09:30:00', '待就诊');

INSERT INTO Registrations (PatientName, DoctorID, ClinicID, RegistrationTime, Status)
VALUES ('王五', 2, 2, '2025-07-26 10:00:00', '已完成');
GO

-- 验证数据插入
SELECT 'Doctors' AS TableName, COUNT(*) AS RecordCount FROM Doctors
UNION ALL
SELECT 'Medicines', COUNT(*) FROM Medicines
UNION ALL
SELECT 'OutpatientClinics', COUNT(*) FROM OutpatientClinics
UNION ALL
SELECT 'Registrations', COUNT(*) FROM Registrations;
GO
