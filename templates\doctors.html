{% extends "base.html" %}

{% block title %}医生管理{% endblock %}

{% block content %}
<div class="table-container">
    <h2>医生列表</h2>
    <div class="mb-3">
        <a href="{{ url_for('add_doctor') }}" class="btn btn-success">添加医生</a>
    </div>
    
    <table class="table table-striped table-hover">
        <thead class="table-light">
            <tr>
                <th>ID</th>
                <th>姓名</th>
                <th>专业</th>
                <th>职称</th>
                <th>联系方式</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for doctor in doctors %}
            <tr>
                <td>{{ doctor[0] }}</td>
                <td>{{ doctor[1] }}</td>
                <td>{{ doctor[2] }}</td>
                <td>{{ doctor[3] }}</td>
                <td>{{ doctor[4] }}</td>
                <td>
                    <a href="{{ url_for('edit_doctor', id=doctor[0]) }}" class="btn btn-sm btn-primary">编辑</a>
                    <a href="{{ url_for('delete_doctor', id=doctor[0]) }}" class="btn btn-sm btn-danger" onclick="return confirm('确定删除吗？')">删除</a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}