{% extends "base.html" %}

{% block title %}药品管理{% endblock %}

{% block content %}
<div class="table-container">
    <h2>药品列表</h2>
    <div class="mb-3">
        <a href="{{ url_for('add_medicine') }}" class="btn btn-success">添加药品</a>
        <a href="{{ url_for('export_medicines') }}" class="btn btn-info">导出Excel</a>
    </div>
    
    <table class="table table-striped table-hover">
        <thead class="table-light">
            <tr>
                <th>ID</th>
                <th>药品名称</th>
                <th>规格</th>
                <th>生产厂家</th>
                <th>价格</th>
                <th>库存</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for medicine in medicines %}
            <tr>
                <td>{{ medicine[0] }}</td>
                <td>{{ medicine[1] }}</td>
                <td>{{ medicine[2] }}</td>
                <td>{{ medicine[3] }}</td>
                <td>{{ "%.2f"|format(medicine[4]) }}</td>
                <td>{{ medicine[5] }}</td>
                <td>
                    <a href="{{ url_for('edit_medicine', id=medicine[0]) }}" class="btn btn-sm btn-primary">编辑</a>
                    <a href="{{ url_for('delete_medicine', id=medicine[0]) }}" class="btn btn-sm btn-danger" onclick="return confirm('确定删除吗？')">删除</a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}