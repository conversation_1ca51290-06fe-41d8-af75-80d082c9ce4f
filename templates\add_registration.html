{% extends "base.html" %}

{% block title %}添加挂号单{% endblock %}

{% block content %}
<div class="table-container">
    <h2>添加挂号单</h2>
    <form method="POST">
        <div class="mb-3">
            <label for="patient_name" class="form-label">患者姓名</label>
            <input type="text" class="form-control" id="patient_name" name="patient_name" required>
        </div>
        <div class="mb-3">
            <label for="doctor_id" class="form-label">医生</label>
            <select class="form-select" id="doctor_id" name="doctor_id" required>
                <option value="">选择医生</option>
                {% for doctor in doctors %}
                <option value="{{ doctor[0] }}">{{ doctor[1] }} - {{ doctor[2] }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="mb-3">
            <label for="clinic_id" class="form-label">门诊</label>
            <select class="form-select" id="clinic_id" name="clinic_id" required>
                <option value="">选择门诊</option>
                {% for clinic in clinics %}
                <option value="{{ clinic[0] }}">{{ clinic[1] }} ({{ clinic[2] }})</option>
                {% endfor %}
            </select>
        </div>
        <div class="mb-3">
            <label for="registration_time" class="form-label">挂号时间</label>
            <input type="datetime-local" class="form-control" id="registration_time" name="registration_time" required>
        </div>
        <div class="mb-3">
            <label for="status" class="form-label">状态</label>
            <select class="form-select" id="status" name="status" required>
                <option value="待就诊">待就诊</option>
                <option value="就诊中">就诊中</option>
                <option value="已完成">已完成</option>
                <option value="已取消">已取消</option>
            </select>
        </div>
        <button type="submit" class="btn btn-primary">提交</button>
        <a href="{{ url_for('registrations') }}" class="btn btn-secondary">取消</a>
    </form>
</div>
{% endblock %}